#!/usr/bin/env python3
"""
Seed script to create default categories for the Retro Notes app.
Run this script to populate the database with some default retro-themed categories.
"""

import os
import sys
from app import create_app
from models import db, Category

def seed_categories():
    """Create default retro-themed categories."""
    
    default_categories = [
        {
            'name': 'Personal',
            'color': '#FF8C42',  # Retro orange
            'description': 'Personal thoughts, diary entries, and private notes'
        },
        {
            'name': 'Work',
            'color': '#9CAF88',  # Retro green
            'description': 'Work-related notes, meetings, and professional tasks'
        },
        {
            'name': 'Ideas',
            'color': '#FFD700',  # Retro yellow
            'description': 'Creative ideas, brainstorming, and inspiration'
        },
        {
            'name': 'Learning',
            'color': '#8B4513',  # Retro brown
            'description': 'Study notes, research, and educational content'
        },
        {
            'name': 'Projects',
            'color': '#CD5C5C',  # Retro red
            'description': 'Project planning, progress tracking, and milestones'
        },
        {
            'name': 'Travel',
            'color': '#D2B48C',  # Retro brown light
            'description': 'Travel plans, memories, and adventure notes'
        }
    ]
    
    app = create_app()
    
    with app.app_context():
        print("🎯 Seeding default categories...")
        
        for cat_data in default_categories:
            # Check if category already exists
            existing = Category.query.filter_by(name=cat_data['name']).first()
            if existing:
                print(f"   ⚠️  Category '{cat_data['name']}' already exists, skipping...")
                continue
            
            # Create new category
            category = Category(
                name=cat_data['name'],
                color=cat_data['color'],
                description=cat_data['description']
            )
            
            db.session.add(category)
            print(f"   ✅ Created category: {cat_data['name']} ({cat_data['color']})")
        
        try:
            db.session.commit()
            print("🎉 Successfully seeded categories!")
            
            # Display all categories
            categories = Category.query.all()
            print(f"\n📋 Total categories in database: {len(categories)}")
            for cat in categories:
                print(f"   • {cat.name} ({cat.color}) - {cat.description}")
                
        except Exception as e:
            db.session.rollback()
            print(f"❌ Error seeding categories: {e}")
            return False
    
    return True

if __name__ == '__main__':
    print("🏷️  Retro Notes - Category Seeder")
    print("=" * 40)
    
    success = seed_categories()
    
    if success:
        print("\n✨ Seeding completed successfully!")
        print("You can now use these categories in your notes.")
    else:
        print("\n💥 Seeding failed!")
        sys.exit(1)
