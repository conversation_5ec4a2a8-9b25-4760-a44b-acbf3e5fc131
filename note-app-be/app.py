import os
from flask import Flask, request, jsonify
from flask_cors import CORS
from config import config
from models import db, Note, Category

def create_app(config_name=None):
    """Application factory pattern."""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')

    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # Enable CORS for all routes
    CORS(app, origins=['http://localhost:3000'])

    # Initialize database
    db.init_app(app)

    # Create tables
    with app.app_context():
        db.create_all()

    # Register routes
    register_routes(app)

    return app

def register_routes(app):
    """Register all application routes."""

    @app.route('/notes', methods=['GET'])
    def get_notes():
        """Get all notes."""
        try:
            notes = Note.query.all()
            return jsonify([note.to_dict() for note in notes])
        except Exception as e:
            return jsonify({'error': 'Failed to retrieve notes'}), 500

    @app.route('/notes', methods=['POST'])
    def create_note():
        """Create a new note."""
        try:
            data = request.get_json()

            if not data or not data.get('title'):
                return jsonify({'error': 'Title is required'}), 400

            # Validate category_id if provided
            category_id = data.get('category_id')
            if category_id:
                category = Category.query.get(category_id)
                if not category:
                    return jsonify({'error': 'Invalid category ID'}), 400

            note = Note(
                title=data.get('title', ''),
                content=data.get('content', ''),
                category_id=category_id,
                is_pinned=data.get('is_pinned', False)
            )
            db.session.add(note)
            db.session.commit()
            return jsonify(note.to_dict()), 201
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to create note'}), 500

    @app.route('/notes/<int:id>', methods=['GET'])
    def get_note(id):
        """Get a specific note by ID."""
        try:
            note = Note.query.get(id)
            if note:
                return jsonify(note.to_dict())
            return jsonify({'error': 'Note not found'}), 404
        except Exception as e:
            return jsonify({'error': 'Failed to retrieve note'}), 500

    @app.route('/notes/<int:id>', methods=['PUT'])
    def update_note(id):
        """Update a specific note."""
        try:
            note = Note.query.get(id)
            if not note:
                return jsonify({'error': 'Note not found'}), 404

            data = request.get_json()
            if not data:
                return jsonify({'error': 'No data provided'}), 400

            # Validate category_id if provided
            if 'category_id' in data:
                category_id = data.get('category_id')
                if category_id:
                    category = Category.query.get(category_id)
                    if not category:
                        return jsonify({'error': 'Invalid category ID'}), 400
                note.category_id = category_id

            note.title = data.get('title', note.title)
            note.content = data.get('content', note.content)
            note.is_pinned = data.get('is_pinned', note.is_pinned)
            db.session.commit()
            return jsonify(note.to_dict())
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to update note'}), 500

    @app.route('/notes/<int:id>', methods=['DELETE'])
    def delete_note(id):
        """Delete a specific note."""
        try:
            note = Note.query.get(id)
            if not note:
                return jsonify({'error': 'Note not found'}), 404

            db.session.delete(note)
            db.session.commit()
            return jsonify({'message': 'Note deleted successfully'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to delete note'}), 500

    # ===== CATEGORY ENDPOINTS =====

    @app.route('/categories', methods=['GET'])
    def get_categories():
        """Get all categories."""
        try:
            categories = Category.query.all()
            return jsonify([category.to_dict() for category in categories])
        except Exception as e:
            return jsonify({'error': 'Failed to retrieve categories'}), 500

    @app.route('/categories', methods=['POST'])
    def create_category():
        """Create a new category."""
        try:
            data = request.get_json()

            if not data or not data.get('name'):
                return jsonify({'error': 'Category name is required'}), 400

            # Check if category name already exists
            existing_category = Category.query.filter_by(name=data.get('name')).first()
            if existing_category:
                return jsonify({'error': 'Category name already exists'}), 400

            category = Category(
                name=data.get('name', ''),
                color=data.get('color', '#FF8C42'),
                description=data.get('description', '')
            )
            db.session.add(category)
            db.session.commit()
            return jsonify(category.to_dict()), 201
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to create category'}), 500

    @app.route('/categories/<int:id>', methods=['GET'])
    def get_category(id):
        """Get a specific category by ID."""
        try:
            category = Category.query.get(id)
            if category:
                return jsonify(category.to_dict())
            return jsonify({'error': 'Category not found'}), 404
        except Exception as e:
            return jsonify({'error': 'Failed to retrieve category'}), 500

    @app.route('/categories/<int:id>', methods=['PUT'])
    def update_category(id):
        """Update a specific category."""
        try:
            category = Category.query.get(id)
            if not category:
                return jsonify({'error': 'Category not found'}), 404

            data = request.get_json()
            if not data:
                return jsonify({'error': 'No data provided'}), 400

            # Check if new name already exists (if name is being changed)
            if 'name' in data and data['name'] != category.name:
                existing_category = Category.query.filter_by(name=data['name']).first()
                if existing_category:
                    return jsonify({'error': 'Category name already exists'}), 400

            category.name = data.get('name', category.name)
            category.color = data.get('color', category.color)
            category.description = data.get('description', category.description)
            db.session.commit()
            return jsonify(category.to_dict())
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to update category'}), 500

    @app.route('/categories/<int:id>', methods=['DELETE'])
    def delete_category(id):
        """Delete a specific category."""
        try:
            category = Category.query.get(id)
            if not category:
                return jsonify({'error': 'Category not found'}), 404

            # Check if category has notes
            if category.notes:
                return jsonify({
                    'error': f'Cannot delete category. It contains {len(category.notes)} notes. Please move or delete the notes first.'
                }), 400

            db.session.delete(category)
            db.session.commit()
            return jsonify({'message': 'Category deleted successfully'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to delete category'}), 500

    @app.route('/notes/by-category/<int:category_id>', methods=['GET'])
    def get_notes_by_category(category_id):
        """Get all notes in a specific category."""
        try:
            category = Category.query.get(category_id)
            if not category:
                return jsonify({'error': 'Category not found'}), 404

            notes = Note.query.filter_by(category_id=category_id).all()
            return jsonify([note.to_dict() for note in notes])
        except Exception as e:
            return jsonify({'error': 'Failed to retrieve notes'}), 500

# Create the application instance
app = create_app()

if __name__ == '__main__':
    app.run(debug=app.config.get('DEBUG', False))