import os
from flask import Flask, request, jsonify
from flask_cors import CORS
from config import config
from models import db, Note, Category

def create_app(config_name=None):
    """Application factory pattern."""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')

    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # Enable CORS for all routes
    CORS(app, origins=['http://localhost:3000'])

    # Initialize database
    db.init_app(app)

    # Create tables
    with app.app_context():
        db.create_all()

    # Register routes
    register_routes(app)

    return app

def register_routes(app):
    """Register all application routes."""

    @app.route('/notes', methods=['GET'])
    def get_notes():
        """Get all notes."""
        try:
            notes = Note.query.all()
            return jsonify([note.to_dict() for note in notes])
        except Exception as e:
            return jsonify({'error': 'Failed to retrieve notes'}), 500

    @app.route('/notes', methods=['POST'])
    def create_note():
        """Create a new note."""
        try:
            data = request.get_json()

            if not data or not data.get('title'):
                return jsonify({'error': 'Title is required'}), 400

            note = Note(
                title=data.get('title', ''),
                content=data.get('content', '')
            )
            db.session.add(note)
            db.session.commit()
            return jsonify(note.to_dict()), 201
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to create note'}), 500

    @app.route('/notes/<int:id>', methods=['GET'])
    def get_note(id):
        """Get a specific note by ID."""
        try:
            note = Note.query.get(id)
            if note:
                return jsonify(note.to_dict())
            return jsonify({'error': 'Note not found'}), 404
        except Exception as e:
            return jsonify({'error': 'Failed to retrieve note'}), 500

    @app.route('/notes/<int:id>', methods=['PUT'])
    def update_note(id):
        """Update a specific note."""
        try:
            note = Note.query.get(id)
            if not note:
                return jsonify({'error': 'Note not found'}), 404

            data = request.get_json()
            if not data:
                return jsonify({'error': 'No data provided'}), 400

            note.title = data.get('title', note.title)
            note.content = data.get('content', note.content)
            db.session.commit()
            return jsonify(note.to_dict())
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to update note'}), 500

    @app.route('/notes/<int:id>', methods=['DELETE'])
    def delete_note(id):
        """Delete a specific note."""
        try:
            note = Note.query.get(id)
            if not note:
                return jsonify({'error': 'Note not found'}), 404

            db.session.delete(note)
            db.session.commit()
            return jsonify({'message': 'Note deleted successfully'})
        except Exception as e:
            db.session.rollback()
            return jsonify({'error': 'Failed to delete note'}), 500

# Create the application instance
app = create_app()

if __name__ == '__main__':
    app.run(debug=app.config.get('DEBUG', False))