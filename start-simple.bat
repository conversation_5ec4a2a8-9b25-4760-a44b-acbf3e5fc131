@echo off
title Retro Notes Launcher
echo.
echo ========================================
echo    🎵 RETRO NOTES - QUICK START 🎵
echo ========================================
echo.

echo 🔧 Starting Backend (Flask)...
start "Backend - Flask" cmd /k "cd note-app-be && python app.py"

echo ⏳ Waiting 3 seconds for backend...
timeout /t 3 /nobreak >nul

echo 🎨 Starting Frontend (Next.js)...
start "Frontend - Next.js" cmd /k "cd note-app-frontend && npm run dev"

echo.
echo ✨ Both services are starting in separate windows!
echo.
echo 📍 Backend:  http://localhost:5000
echo 📍 Frontend: http://localhost:3000
echo.
echo 💡 Close this window when done.
echo.
pause
