@echo off
echo 🧹 Cleaning up and starting Retro Notes...
echo ==========================================

:: Kill any existing processes on ports 3000 and 5000
echo 🔄 Stopping existing processes...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3000') do (
    if not "%%a"=="0" (
        taskkill /F /PID %%a >nul 2>&1
    )
)
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000') do (
    if not "%%a"=="0" (
        taskkill /F /PID %%a >nul 2>&1
    )
)

:: Clean frontend build artifacts
echo 🧹 Cleaning frontend build files...
cd note-app-frontend
if exist ".next" rmdir /s /q ".next" >nul 2>&1
if exist "package-lock.json" del "package-lock.json" >nul 2>&1
cd ..

:: Clean any duplicate lockfiles
echo 🧹 Removing duplicate lockfiles...
if exist "package-lock.json" del "package-lock.json" >nul 2>&1

echo ✅ Cleanup completed!
echo.

:: Start backend
echo 🔧 Starting Backend (Flask)...
start "Retro Notes Backend" cmd /k "cd note-app-be && python app.py"

:: Wait for backend to start
echo ⏳ Waiting for backend to initialize...
timeout /t 5 /nobreak >nul

:: Start frontend with clean install
echo 🎨 Starting Frontend (Next.js)...
cd note-app-frontend
echo 📦 Installing/updating dependencies...
npm install >nul 2>&1
echo ✅ Dependencies ready

echo 🚀 Starting development server...
npm run dev

echo.
echo 🎉 Retro Notes is running!
echo 📍 Backend:  http://localhost:5000
echo 📍 Frontend: http://localhost:3000
echo.
pause
