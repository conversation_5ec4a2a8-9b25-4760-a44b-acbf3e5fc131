'use client';

import { useState, useEffect } from 'react';
import { ChevronDown, Plus, Tag, X } from 'lucide-react';
import { Category } from '@/lib/types';
import { useNotesStore } from '@/lib/store';
import { cn } from '@/lib/utils';

interface CategorySelectorProps {
  selectedCategoryId?: number;
  onCategoryChange: (categoryId: number | undefined) => void;
  disabled?: boolean;
  className?: string;
}

export function CategorySelector({ 
  selectedCategoryId, 
  onCategoryChange, 
  disabled = false,
  className 
}: CategorySelectorProps) {
  const { categories, fetchCategories, isLoading } = useNotesStore();
  const [isOpen, setIsOpen] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);

  useEffect(() => {
    if (categories.length === 0) {
      fetchCategories();
    }
  }, [categories.length, fetchCategories]);

  const selectedCategory = categories.find(cat => cat.id === selectedCategoryId);

  const handleCategorySelect = (categoryId: number | undefined) => {
    onCategoryChange(categoryId);
    setIsOpen(false);
  };

  const handleClearCategory = (e: React.MouseEvent) => {
    e.stopPropagation();
    onCategoryChange(undefined);
  };

  return (
    <div className={cn('relative', className)}>
      {/* Category Selector Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        disabled={disabled || isLoading}
        className={cn(
          'retro-input flex items-center justify-between w-full',
          'hover:border-retro-orange focus:border-retro-orange',
          disabled && 'opacity-50 cursor-not-allowed',
          isOpen && 'border-retro-orange ring-2 ring-retro-orange/20'
        )}
      >
        <div className="flex items-center space-x-2 flex-1 min-w-0">
          <Tag className="w-4 h-4 text-retro-brown flex-shrink-0" />
          {selectedCategory ? (
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <div 
                className="w-3 h-3 rounded-full flex-shrink-0"
                style={{ backgroundColor: selectedCategory.color }}
              />
              <span className="retro-text truncate">{selectedCategory.name}</span>
              <button
                type="button"
                onClick={handleClearCategory}
                className="p-1 hover:bg-retro-brown/10 rounded-full flex-shrink-0"
                title="Clear category"
              >
                <X className="w-3 h-3" />
              </button>
            </div>
          ) : (
            <span className="retro-text opacity-60">Select a category...</span>
          )}
        </div>
        <ChevronDown 
          className={cn(
            'w-4 h-4 text-retro-brown transition-transform flex-shrink-0',
            isOpen && 'rotate-180'
          )} 
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-1 z-50">
          <div className="retro-card border border-retro-brown-light shadow-retro max-h-64 overflow-y-auto">
            {/* No Category Option */}
            <button
              type="button"
              onClick={() => handleCategorySelect(undefined)}
              className={cn(
                'w-full px-4 py-3 text-left hover:bg-retro-orange/10 transition-colors',
                'border-b border-retro-brown-light/30',
                !selectedCategoryId && 'bg-retro-orange/20'
              )}
            >
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 rounded-full border-2 border-retro-brown-light" />
                <span className="retro-text">No Category</span>
              </div>
            </button>

            {/* Category Options */}
            {categories.map((category) => (
              <button
                key={category.id}
                type="button"
                onClick={() => handleCategorySelect(category.id)}
                className={cn(
                  'w-full px-4 py-3 text-left hover:bg-retro-orange/10 transition-colors',
                  'border-b border-retro-brown-light/30 last:border-b-0',
                  selectedCategoryId === category.id && 'bg-retro-orange/20'
                )}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 flex-1 min-w-0">
                    <div 
                      className="w-3 h-3 rounded-full flex-shrink-0"
                      style={{ backgroundColor: category.color }}
                    />
                    <div className="flex-1 min-w-0">
                      <div className="retro-text font-semibold truncate">
                        {category.name}
                      </div>
                      {category.description && (
                        <div className="text-xs opacity-60 truncate">
                          {category.description}
                        </div>
                      )}
                    </div>
                  </div>
                  <span className="text-xs opacity-60 flex-shrink-0 ml-2">
                    {category.notes_count} notes
                  </span>
                </div>
              </button>
            ))}

            {/* Create New Category Button */}
            <button
              type="button"
              onClick={() => {
                setShowCreateForm(true);
                setIsOpen(false);
              }}
              className="w-full px-4 py-3 text-left hover:bg-retro-green/10 transition-colors border-t border-retro-brown-light"
            >
              <div className="flex items-center space-x-2">
                <Plus className="w-4 h-4 text-retro-green" />
                <span className="retro-text text-retro-green font-semibold">
                  Create New Category
                </span>
              </div>
            </button>
          </div>
        </div>
      )}

      {/* Create Category Form Modal */}
      {showCreateForm && (
        <CreateCategoryModal
          onClose={() => setShowCreateForm(false)}
          onCategoryCreated={(category) => {
            onCategoryChange(category.id);
            setShowCreateForm(false);
          }}
        />
      )}
    </div>
  );
}

interface CreateCategoryModalProps {
  onClose: () => void;
  onCategoryCreated: (category: Category) => void;
}

function CreateCategoryModal({ onClose, onCategoryCreated }: CreateCategoryModalProps) {
  const { createCategory, isLoading } = useNotesStore();
  const [name, setName] = useState('');
  const [color, setColor] = useState('#FF8C42');
  const [description, setDescription] = useState('');
  const [error, setError] = useState('');

  const retroColors = [
    '#FF8C42', // Orange
    '#9CAF88', // Green
    '#FFD700', // Yellow
    '#8B4513', // Brown
    '#CD5C5C', // Red
    '#D2B48C', // Brown Light
    '#F5E6D3', // Beige
    '#7A8471', // Green Dark
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!name.trim()) {
      setError('Category name is required');
      return;
    }

    try {
      const category = await createCategory({
        name: name.trim(),
        color,
        description: description.trim() || undefined
      });
      onCategoryCreated(category);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create category');
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="retro-card max-w-md w-full">
        <div className="flex items-center justify-between mb-6">
          <h3 className="retro-heading text-xl">Create New Category</h3>
          <button
            type="button"
            onClick={onClose}
            className="p-2 hover:bg-retro-brown/10 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Name Field */}
          <div>
            <label className="block retro-text font-semibold mb-2">
              Category Name *
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter category name..."
              className="retro-input"
              maxLength={100}
              disabled={isLoading}
            />
          </div>

          {/* Color Picker */}
          <div>
            <label className="block retro-text font-semibold mb-2">
              Color
            </label>
            <div className="flex flex-wrap gap-2">
              {retroColors.map((colorOption) => (
                <button
                  key={colorOption}
                  type="button"
                  onClick={() => setColor(colorOption)}
                  className={cn(
                    'w-8 h-8 rounded-full border-2 transition-all',
                    color === colorOption 
                      ? 'border-retro-brown scale-110' 
                      : 'border-retro-brown-light hover:scale-105'
                  )}
                  style={{ backgroundColor: colorOption }}
                  title={colorOption}
                />
              ))}
            </div>
          </div>

          {/* Description Field */}
          <div>
            <label className="block retro-text font-semibold mb-2">
              Description (Optional)
            </label>
            <textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Brief description of this category..."
              className="retro-input resize-none"
              rows={3}
              maxLength={200}
              disabled={isLoading}
            />
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-retro-red/20 border border-retro-red rounded-lg">
              <p className="text-retro-red text-sm font-mono">{error}</p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              disabled={isLoading}
              className="retro-button-secondary flex-1"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isLoading || !name.trim()}
              className="retro-button flex-1"
            >
              {isLoading ? 'Creating...' : 'Create Category'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
