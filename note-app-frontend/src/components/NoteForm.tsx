'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Save, X, FileText, Pin } from 'lucide-react';
import { Note, CreateNoteRequest, UpdateNoteRequest } from '@/lib/types';
import { isValidNote, cn } from '@/lib/utils';
import { CategorySelector } from './CategorySelector';

interface NoteFormProps {
  note?: Note;
  onSubmit: (data: any) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  mode: 'create' | 'edit';
}

export function NoteForm({
  note,
  onSubmit,
  onCancel,
  isLoading = false,
  mode
}: NoteFormProps) {
  const [title, setTitle] = useState(note?.title || '');
  const [content, setContent] = useState(note?.content || '');
  const [categoryId, setCategoryId] = useState<number | undefined>(note?.category_id);
  const [isPinned, setIsPinned] = useState(note?.is_pinned || false);
  const [errors, setErrors] = useState<{ title?: string; content?: string }>({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const router = useRouter();

  // Track changes
  useEffect(() => {
    const hasChanges =
      title !== (note?.title || '') ||
      content !== (note?.content || '') ||
      categoryId !== note?.category_id ||
      isPinned !== (note?.is_pinned || false);
    setHasUnsavedChanges(hasChanges);
  }, [title, content, categoryId, isPinned, note]);

  // Auto-save functionality (optional)
  useEffect(() => {
    if (mode === 'edit' && hasUnsavedChanges && note) {
      const autoSaveTimer = setTimeout(() => {
        if (isValidNote(title, content)) {
          // Auto-save logic could go here
          console.log('Auto-saving...');
        }
      }, 5000); // Auto-save after 5 seconds of inactivity

      return () => clearTimeout(autoSaveTimer);
    }
  }, [title, content, hasUnsavedChanges, mode, note]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate
    const newErrors: { title?: string; content?: string } = {};

    if (!title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setErrors({});

    try {
      await onSubmit({
        title: title.trim(),
        content: content.trim(),
        category_id: categoryId,
        is_pinned: isPinned,
      });
    } catch (error) {
      console.error('Failed to save note:', error);
    }
  };

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        if (e.key === 's') {
          e.preventDefault();
          if (isValidNote(title, content) && !isLoading) {
            handleSubmit(e as any);
          }
        } else if (e.key === 'Escape') {
          e.preventDefault();
          onCancel();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [title, content, isLoading, onCancel]);

  // Warn about unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="retro-card mb-8">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-retro-orange/20 rounded-lg">
              <FileText className="w-6 h-6 text-retro-orange" />
            </div>
            <div>
              <h1 className="retro-heading text-2xl">
                {mode === 'create' ? 'Create New Note' : 'Edit Note'}
              </h1>
              <p className="retro-text text-sm opacity-75">
                {mode === 'create'
                  ? 'Craft your thoughts with vintage style'
                  : 'Update your note with care'
                }
              </p>
            </div>
          </div>

          {hasUnsavedChanges && (
            <div className="px-3 py-1 bg-retro-yellow/20 text-retro-brown rounded-full text-sm font-mono">
              Unsaved changes
            </div>
          )}
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Title Field */}
        <div className="retro-card">
          <label htmlFor="title" className="block retro-heading text-lg mb-3">
            Note Title *
          </label>
          <input
            id="title"
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter a captivating title..."
            className={cn(
              'retro-input',
              errors.title && 'border-retro-red focus:border-retro-red focus:ring-retro-red/20'
            )}
            disabled={isLoading}
            maxLength={255}
          />
          {errors.title && (
            <p className="mt-2 text-retro-red text-sm font-mono">
              {errors.title}
            </p>
          )}
          <p className="mt-2 retro-text text-sm opacity-60">
            {title.length}/255 characters
          </p>
        </div>

        {/* Content Field */}
        <div className="retro-card">
          <label htmlFor="content" className="block retro-heading text-lg mb-3">
            Note Content
          </label>
          <textarea
            id="content"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            placeholder="Pour your thoughts onto this digital paper..."
            className={cn(
              'retro-textarea min-h-[300px]',
              errors.content && 'border-retro-red focus:border-retro-red focus:ring-retro-red/20'
            )}
            disabled={isLoading}
          />
          {errors.content && (
            <p className="mt-2 text-retro-red text-sm font-mono">
              {errors.content}
            </p>
          )}
          <p className="mt-2 retro-text text-sm opacity-60">
            {content.length} characters
          </p>
        </div>

        {/* Category and Options */}
        <div className="retro-card">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Category Selector */}
            <div>
              <label className="block retro-heading text-lg mb-3">
                Category
              </label>
              <CategorySelector
                selectedCategoryId={categoryId}
                onCategoryChange={setCategoryId}
                disabled={isLoading}
              />
              <p className="mt-2 retro-text text-sm opacity-60">
                Organize your notes with categories
              </p>
            </div>

            {/* Pin Toggle */}
            <div>
              <label className="block retro-heading text-lg mb-3">
                Options
              </label>
              <div className="space-y-3">
                <label className="flex items-center space-x-3 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={isPinned}
                    onChange={(e) => setIsPinned(e.target.checked)}
                    disabled={isLoading}
                    className="sr-only"
                  />
                  <div className={cn(
                    'relative w-12 h-6 rounded-full transition-colors duration-200',
                    isPinned ? 'bg-retro-orange' : 'bg-retro-brown-light'
                  )}>
                    <div className={cn(
                      'absolute top-1 w-4 h-4 bg-white rounded-full transition-transform duration-200',
                      isPinned ? 'translate-x-7' : 'translate-x-1'
                    )} />
                  </div>
                  <div className="flex items-center space-x-2">
                    <Pin className={cn(
                      'w-4 h-4 transition-colors',
                      isPinned ? 'text-retro-orange' : 'text-retro-brown'
                    )} />
                    <span className="retro-text font-semibold">
                      Pin this note
                    </span>
                  </div>
                </label>
                <p className="retro-text text-sm opacity-60 ml-15">
                  Pinned notes appear at the top
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="retro-card">
          <div className="flex items-center justify-between">
            <div className="retro-text text-sm opacity-75">
              <p>💡 <strong>Pro tip:</strong> Use Ctrl+S to save, Escape to cancel</p>
            </div>

            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={onCancel}
                disabled={isLoading}
                className={cn(
                  'px-6 py-3 rounded-lg font-semibold transition-all duration-200',
                  'bg-retro-brown-light text-retro-brown border-2 border-retro-brown-light',
                  'hover:bg-retro-beige hover:shadow-retro',
                  'disabled:opacity-50 disabled:cursor-not-allowed',
                  'focus:outline-none focus:ring-2 focus:ring-retro-brown/20'
                )}
              >
                <X className="w-4 h-4 inline mr-2" />
                Cancel
              </button>

              <button
                type="submit"
                disabled={isLoading || !isValidNote(title, content)}
                className={cn(
                  'retro-button',
                  'focus:outline-none focus:ring-2 focus:ring-retro-orange/20'
                )}
              >
                {isLoading ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin inline mr-2" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 inline mr-2" />
                    {mode === 'create' ? 'Create Note' : 'Update Note'}
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
