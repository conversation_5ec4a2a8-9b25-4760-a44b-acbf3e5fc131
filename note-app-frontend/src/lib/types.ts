export interface Category {
  id: number;
  name: string;
  color: string;
  description?: string;
  created_at: string;
  notes_count: number;
}

export interface Note {
  id: number;
  title: string;
  content: string;
  category_id?: number;
  category?: Category;
  is_pinned: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateNoteRequest {
  title: string;
  content: string;
  category_id?: number;
  is_pinned?: boolean;
}

export interface UpdateNoteRequest {
  title?: string;
  content?: string;
  category_id?: number;
  is_pinned?: boolean;
}

export interface CreateCategoryRequest {
  name: string;
  color?: string;
  description?: string;
}

export interface UpdateCategoryRequest {
  name?: string;
  color?: string;
  description?: string;
}

export interface ApiError {
  error: string;
}

export interface DeleteNoteResponse {
  message: string;
}

export interface SearchFilters {
  query: string;
  sortBy: 'created_at' | 'updated_at' | 'title';
  sortOrder: 'asc' | 'desc';
  categoryId?: number;
  showPinnedOnly?: boolean;
}

export interface Theme {
  isDark: boolean;
}
