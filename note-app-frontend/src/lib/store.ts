import { create } from 'zustand';
import {
  Note,
  CreateNoteRequest,
  UpdateNoteRequest,
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  SearchFilters,
  Theme
} from './types';
import { apiClient } from './api';

interface NotesState {
  // Data
  notes: Note[];
  currentNote: Note | null;
  categories: Category[];

  // UI State
  isLoading: boolean;
  error: string | null;
  searchFilters: SearchFilters;
  theme: Theme;

  // Note Actions
  fetchNotes: () => Promise<void>;
  fetchNote: (id: number) => Promise<void>;
  createNote: (data: CreateNoteRequest) => Promise<Note>;
  updateNote: (id: number, data: UpdateNoteRequest) => Promise<Note>;
  deleteNote: (id: number) => Promise<void>;

  // Category Actions
  fetchCategories: () => Promise<void>;
  createCategory: (data: CreateCategoryRequest) => Promise<Category>;
  updateCategory: (id: number, data: UpdateCategoryRequest) => Promise<Category>;
  deleteCategory: (id: number) => Promise<void>;

  // UI Actions
  setError: (error: string | null) => void;
  setSearchFilters: (filters: Partial<SearchFilters>) => void;
  toggleTheme: () => void;
  clearCurrentNote: () => void;
}

export const useNotesStore = create<NotesState>((set, get) => ({
  // Initial state
  notes: [],
  currentNote: null,
  categories: [],
  isLoading: false,
  error: null,
  searchFilters: {
    query: '',
    sortBy: 'updated_at',
    sortOrder: 'desc',
    categoryId: undefined,
    showPinnedOnly: false,
  },
  theme: {
    isDark: false,
  },

  // Data actions
  fetchNotes: async () => {
    set({ isLoading: true, error: null });
    try {
      const notes = await apiClient.getNotes();
      set({ notes, isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch notes',
        isLoading: false
      });
    }
  },

  fetchNote: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      const note = await apiClient.getNote(id);
      set({ currentNote: note, isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch note',
        isLoading: false
      });
    }
  },

  createNote: async (data: CreateNoteRequest) => {
    set({ isLoading: true, error: null });
    try {
      const newNote = await apiClient.createNote(data);
      set(state => ({
        notes: [newNote, ...state.notes],
        isLoading: false
      }));
      return newNote;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to create note',
        isLoading: false
      });
      throw error;
    }
  },

  updateNote: async (id: number, data: UpdateNoteRequest) => {
    set({ isLoading: true, error: null });
    try {
      const updatedNote = await apiClient.updateNote(id, data);
      set(state => ({
        notes: state.notes.map(note =>
          note.id === id ? updatedNote : note
        ),
        currentNote: state.currentNote?.id === id ? updatedNote : state.currentNote,
        isLoading: false
      }));
      return updatedNote;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to update note',
        isLoading: false
      });
      throw error;
    }
  },

  deleteNote: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      await apiClient.deleteNote(id);
      set(state => ({
        notes: state.notes.filter(note => note.id !== id),
        currentNote: state.currentNote?.id === id ? null : state.currentNote,
        isLoading: false
      }));
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to delete note',
        isLoading: false
      });
      throw error;
    }
  },

  // UI actions
  setError: (error: string | null) => set({ error }),

  setSearchFilters: (filters: Partial<SearchFilters>) =>
    set(state => ({
      searchFilters: { ...state.searchFilters, ...filters }
    })),

  toggleTheme: () =>
    set(state => ({
      theme: { isDark: !state.theme.isDark }
    })),

  clearCurrentNote: () => set({ currentNote: null }),

  // ===== CATEGORY ACTIONS =====

  fetchCategories: async () => {
    set({ isLoading: true, error: null });
    try {
      const categories = await apiClient.getCategories();
      set({ categories, isLoading: false });
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to fetch categories',
        isLoading: false
      });
    }
  },

  createCategory: async (data: CreateCategoryRequest) => {
    set({ isLoading: true, error: null });
    try {
      const category = await apiClient.createCategory(data);
      set(state => ({
        categories: [...state.categories, category],
        isLoading: false
      }));
      return category;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to create category',
        isLoading: false
      });
      throw error;
    }
  },

  updateCategory: async (id: number, data: UpdateCategoryRequest) => {
    set({ isLoading: true, error: null });
    try {
      const updatedCategory = await apiClient.updateCategory(id, data);
      set(state => ({
        categories: state.categories.map(cat =>
          cat.id === id ? updatedCategory : cat
        ),
        isLoading: false
      }));
      return updatedCategory;
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to update category',
        isLoading: false
      });
      throw error;
    }
  },

  deleteCategory: async (id: number) => {
    set({ isLoading: true, error: null });
    try {
      await apiClient.deleteCategory(id);
      set(state => ({
        categories: state.categories.filter(cat => cat.id !== id),
        isLoading: false
      }));
    } catch (error) {
      set({
        error: error instanceof Error ? error.message : 'Failed to delete category',
        isLoading: false
      });
      throw error;
    }
  },
}));
