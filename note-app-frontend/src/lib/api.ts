import {
  Note,
  CreateNoteRequest,
  UpdateNoteRequest,
  Category,
  CreateCategoryRequest,
  UpdateCategoryRequest,
  ApiError,
  DeleteNoteResponse
} from './types';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000';

class ApiClient {
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        const errorData: ApiError = await response.json().catch(() => ({
          error: `HTTP ${response.status}: ${response.statusText}`,
        }));
        throw new Error(errorData.error || 'An error occurred');
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Network error occurred');
    }
  }

  // Get all notes
  async getNotes(): Promise<Note[]> {
    return this.request<Note[]>('/notes');
  }

  // Get a specific note by ID
  async getNote(id: number): Promise<Note> {
    return this.request<Note>(`/notes/${id}`);
  }

  // Create a new note
  async createNote(data: CreateNoteRequest): Promise<Note> {
    return this.request<Note>('/notes', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Update an existing note
  async updateNote(id: number, data: UpdateNoteRequest): Promise<Note> {
    return this.request<Note>(`/notes/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Delete a note
  async deleteNote(id: number): Promise<DeleteNoteResponse> {
    return this.request<DeleteNoteResponse>(`/notes/${id}`, {
      method: 'DELETE',
    });
  }

  // ===== CATEGORY METHODS =====

  // Get all categories
  async getCategories(): Promise<Category[]> {
    return this.request<Category[]>('/categories');
  }

  // Get a specific category by ID
  async getCategory(id: number): Promise<Category> {
    return this.request<Category>(`/categories/${id}`);
  }

  // Create a new category
  async createCategory(data: CreateCategoryRequest): Promise<Category> {
    return this.request<Category>('/categories', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // Update an existing category
  async updateCategory(id: number, data: UpdateCategoryRequest): Promise<Category> {
    return this.request<Category>(`/categories/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Delete a category
  async deleteCategory(id: number): Promise<DeleteNoteResponse> {
    return this.request<DeleteNoteResponse>(`/categories/${id}`, {
      method: 'DELETE',
    });
  }

  // Get notes by category
  async getNotesByCategory(categoryId: number): Promise<Note[]> {
    return this.request<Note[]>(`/notes/by-category/${categoryId}`);
  }
}

export const apiClient = new ApiClient();
