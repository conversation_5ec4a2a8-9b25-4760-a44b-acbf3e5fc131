'use client';

import { useEffect, useMemo } from 'react';
import Link from 'next/link';
import { PlusCircle, BookOpen, Search as SearchIcon } from 'lucide-react';
import { useNotesStore } from '@/lib/store';
import { NoteCard } from '@/components/NoteCard';
import { SearchBar } from '@/components/SearchBar';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { cn } from '@/lib/utils';

export default function HomePage() {
  const {
    notes,
    categories,
    isLoading,
    error,
    searchFilters,
    fetchNotes,
    fetchCategories,
    deleteNote,
    setError
  } = useNotesStore();

  // Fetch notes on mount
  useEffect(() => {
    fetchNotes();
  }, [fetchNotes]);

  // Filter and sort notes based on search criteria
  const filteredNotes = useMemo(() => {
    let filtered = [...notes];

    // Apply search filter
    if (searchFilters.query) {
      const query = searchFilters.query.toLowerCase();
      filtered = filtered.filter(note =>
        note.title.toLowerCase().includes(query) ||
        note.content.toLowerCase().includes(query)
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (searchFilters.sortBy) {
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'created_at':
          aValue = new Date(a.created_at).getTime();
          bValue = new Date(b.created_at).getTime();
          break;
        case 'updated_at':
        default:
          aValue = new Date(a.updated_at).getTime();
          bValue = new Date(b.updated_at).getTime();
          break;
      }

      if (searchFilters.sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [notes, searchFilters]);

  const handleDeleteNote = async (id: number) => {
    try {
      await deleteNote(id);
    } catch (error) {
      console.error('Failed to delete note:', error);
    }
  };

  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="retro-card text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="p-4 bg-retro-orange/20 rounded-full">
            <BookOpen className="w-12 h-12 text-retro-orange" />
          </div>
        </div>

        <h1 className="retro-heading text-4xl md:text-5xl mb-4 typewriter">
          Welcome to Retro Notes
        </h1>

        <p className="retro-text text-lg mb-8 max-w-2xl mx-auto">
          Step back in time with our vintage-inspired note-taking experience.
          Capture your thoughts with the charm of yesteryear and the convenience of today.
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            href="/notes/create"
            className="retro-button inline-flex items-center justify-center"
          >
            <PlusCircle className="w-5 h-5 mr-2" />
            Create Your First Note
          </Link>

          {notes.length > 0 && (
            <button
              onClick={() => document.getElementById('search-section')?.scrollIntoView({ behavior: 'smooth' })}
              className="retro-button retro-button-secondary inline-flex items-center justify-center"
            >
              <SearchIcon className="w-5 h-5 mr-2" />
              Browse Notes
            </button>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="retro-error">
          <p className="font-semibold mb-2">Oops! Something went wrong:</p>
          <p>{error}</p>
          <button
            onClick={() => {
              setError(null);
              fetchNotes();
            }}
            className="mt-3 retro-button text-sm"
          >
            Try Again
          </button>
        </div>
      )}

      {/* Loading State */}
      {isLoading && notes.length === 0 && (
        <div className="retro-card text-center">
          <LoadingSpinner size="lg" />
          <p className="retro-text mt-4">Loading your vintage notes...</p>
        </div>
      )}

      {/* Notes Section */}
      {!isLoading && notes.length > 0 && (
        <div id="search-section" className="space-y-8">
          {/* Search and Filter */}
          <SearchBar />

          {/* Notes Grid */}
          {filteredNotes.length > 0 ? (
            <>
              <div className="flex items-center justify-between">
                <h2 className="retro-heading text-2xl">
                  Your Notes Collection
                </h2>
                <div className="retro-text text-sm opacity-75">
                  {filteredNotes.length} of {notes.length} notes
                  {searchFilters.query && ` matching "${searchFilters.query}"`}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredNotes.map((note) => (
                  <NoteCard
                    key={note.id}
                    note={note}
                    onDelete={handleDeleteNote}
                  />
                ))}
              </div>
            </>
          ) : (
            <div className="retro-card text-center">
              <SearchIcon className="w-16 h-16 text-retro-brown-light mx-auto mb-4" />
              <h3 className="retro-heading text-xl mb-2">
                No notes found
              </h3>
              <p className="retro-text mb-6">
                {searchFilters.query
                  ? `No notes match your search for "${searchFilters.query}"`
                  : 'Try adjusting your search criteria'
                }
              </p>
              <button
                onClick={() => {
                  const { setSearchFilters } = useNotesStore.getState();
                  setSearchFilters({ query: '', sortBy: 'updated_at', sortOrder: 'desc' });
                }}
                className="retro-button retro-button-secondary"
              >
                Clear Search
              </button>
            </div>
          )}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && notes.length === 0 && !error && (
        <div className="retro-card text-center">
          <div className="p-8 bg-retro-orange/10 rounded-full w-32 h-32 mx-auto mb-6 flex items-center justify-center">
            <BookOpen className="w-16 h-16 text-retro-orange" />
          </div>

          <h2 className="retro-heading text-2xl mb-4">
            Your Note Collection Awaits
          </h2>

          <p className="retro-text text-lg mb-8 max-w-md mx-auto">
            Ready to start your vintage note-taking journey?
            Create your first note and begin capturing your thoughts in retro style.
          </p>

          <Link
            href="/notes/create"
            className="retro-button inline-flex items-center"
          >
            <PlusCircle className="w-5 h-5 mr-2" />
            Create Your First Note
          </Link>
        </div>
      )}
    </div>
  );
}
