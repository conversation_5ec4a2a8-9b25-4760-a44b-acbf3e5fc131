# 🏷️ Categories & Tags System - Feature Documentation

## 📋 Tổng quan

Hệ thống Categories/Tags đã được triển khai thành công cho Retro Notes, cho phép người dùng tổ chức và phân loại ghi chú một cách hiệu quả với phong cách retro đặc trưng.

## ✨ Tính năng đã triển khai

### 🎯 Backend Features

#### 1. **Category Model**
- **Thuộc tính:**
  - `id`: ID duy nhất
  - `name`: Tên category (unique, max 100 ký tự)
  - `color`: <PERSON><PERSON><PERSON> sắc retro (hex code, mặc định #FF8C42)
  - `description`: <PERSON><PERSON> tả tùy chọn
  - `created_at`: Thời gian tạo
  - `notes_count`: Số lượng notes trong category

#### 2. **Note Model Updates**
- **Thuộc tính mới:**
  - `category_id`: Foreign key đến Category
  - `is_pinned`: <PERSON><PERSON><PERSON> dấu note quan trọng
  - `category`: Relationship object với thông tin category

#### 3. **API Endpoints**

##### Categories Management
```http
GET    /categories              # Lấy tất cả categories
POST   /categories              # Tạo category mới
GET    /categories/{id}         # Lấy category theo ID
PUT    /categories/{id}         # Cập nhật category
DELETE /categories/{id}         # Xóa category
GET    /notes/by-category/{id}  # Lấy notes theo category
```

##### Notes với Categories
```http
POST /notes
{
  "title": "My Note",
  "content": "Content here",
  "category_id": 1,        # Tùy chọn
  "is_pinned": false       # Tùy chọn
}

PUT /notes/{id}
{
  "title": "Updated Note",
  "category_id": 2,        # Có thể thay đổi category
  "is_pinned": true        # Có thể pin/unpin
}
```

#### 4. **Default Categories**
6 categories mặc định với màu sắc retro:
- **Personal** (#FF8C42) - Orange
- **Work** (#9CAF88) - Green  
- **Ideas** (#FFD700) - Yellow
- **Learning** (#8B4513) - Brown
- **Projects** (#CD5C5C) - Red
- **Travel** (#D2B48C) - Brown Light

### 🎨 Frontend Features

#### 1. **CategorySelector Component**
- **Dropdown selector** với retro styling
- **Color picker** cho categories mới
- **Inline category creation** 
- **Search trong categories**
- **"No Category" option**

#### 2. **NoteForm Updates**
- **Category selection** khi tạo/chỉnh sửa note
- **Pin toggle** với retro switch design
- **Real-time validation**
- **Unsaved changes tracking**

#### 3. **NoteCard Enhancements**
- **Category badge** với màu sắc tương ứng
- **Pin icon** cho pinned notes
- **Category trong footer**
- **Hover effects** retro

#### 4. **Advanced Filtering**
- **Filter theo category**
- **Filter chỉ pinned notes**
- **Search trong category names**
- **Pinned notes luôn hiển thị đầu tiên**

#### 5. **Store Management**
- **Categories state management**
- **CRUD operations** cho categories
- **Error handling**
- **Loading states**

## 🚀 Cách sử dụng

### 1. **Khởi động ứng dụng**
```bash
# Sử dụng script tự động
./start.bat

# Hoặc thủ công
cd note-app-be && python app.py
cd note-app-frontend && npm run dev
```

### 2. **Seed default categories**
```bash
cd note-app-be
python seed_categories.py
```

### 3. **Tạo note với category**
1. Vào **Create New Note**
2. Chọn **Category** từ dropdown
3. Bật **Pin toggle** nếu cần
4. Viết nội dung và **Save**

### 4. **Quản lý categories**
1. Trong **CategorySelector**, click **"Create New Category"**
2. Nhập tên, chọn màu, thêm mô tả
3. Click **"Create Category"**

### 5. **Filter và search**
1. Sử dụng **SearchBar** để tìm theo tên, nội dung, hoặc category
2. Filter theo category cụ thể
3. Toggle **"Show Pinned Only"**

## 🎨 Design System

### **Retro Color Palette**
```css
--retro-orange: #FF8C42      /* Primary accent */
--retro-green: #9CAF88       /* Success/Work */
--retro-yellow: #FFD700      /* Warning/Ideas */
--retro-brown: #8B4513       /* Text/Learning */
--retro-red: #CD5C5C         /* Error/Projects */
--retro-brown-light: #D2B48C /* Subtle/Travel */
```

### **Component Styling**
- **Rounded corners** (8px)
- **Drop shadows** retro-style
- **Paper texture** backgrounds
- **Vintage typography** (Courier, Georgia)
- **Smooth transitions** (200ms)

## 🔧 Technical Details

### **Database Schema**
```sql
-- Categories table
CREATE TABLE categories (
    id INTEGER PRIMARY KEY,
    name VARCHAR(100) UNIQUE NOT NULL,
    color VARCHAR(7) DEFAULT '#FF8C42',
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Notes table updates
ALTER TABLE notes ADD COLUMN category_id INTEGER;
ALTER TABLE notes ADD COLUMN is_pinned BOOLEAN DEFAULT FALSE;
ALTER TABLE notes ADD FOREIGN KEY (category_id) REFERENCES categories(id);
```

### **API Response Format**
```json
{
  "id": 1,
  "title": "My Note",
  "content": "Note content",
  "category_id": 2,
  "category": {
    "id": 2,
    "name": "Work",
    "color": "#9CAF88",
    "description": "Work-related notes",
    "notes_count": 5
  },
  "is_pinned": true,
  "created_at": "2025-01-15T10:30:00.000Z",
  "updated_at": "2025-01-15T10:30:00.000Z"
}
```

## 🧪 Testing

### **Manual Testing Checklist**
- [ ] Tạo category mới
- [ ] Chỉnh sửa category
- [ ] Xóa category (với validation)
- [ ] Tạo note với category
- [ ] Chuyển note sang category khác
- [ ] Pin/unpin notes
- [ ] Filter theo category
- [ ] Search trong categories
- [ ] Responsive design

### **API Testing**
```bash
# Test category creation
curl -X POST http://localhost:5000/categories \
  -H "Content-Type: application/json" \
  -d '{"name":"Test","color":"#FF0000","description":"Test category"}'

# Test note with category
curl -X POST http://localhost:5000/notes \
  -H "Content-Type: application/json" \
  -d '{"title":"Test Note","content":"Content","category_id":1,"is_pinned":true}'
```

## 🎯 Kết quả đạt được

✅ **Categories/Tags System** - Hoàn thành 100%
✅ **Note Pinning/Favorites** - Hoàn thành 100%  
✅ **Advanced Filtering** - Hoàn thành 100%
✅ **Retro UI/UX** - Hoàn thành 100%
✅ **API Integration** - Hoàn thành 100%
✅ **Error Handling** - Hoàn thành 100%

## 🔮 Tiếp theo

Sẵn sàng triển khai các tính năng tiếp theo:
1. **Note Templates** - Pre-defined note formats
2. **Rich Text Markdown Editor** - Enhanced editing experience  
3. **Statistics Dashboard** - Analytics và insights
4. **Export/Backup** - Data portability
5. **Typewriter Sound Effects** - Audio feedback
6. **Vintage Photo Attachments** - Media support

---

🎉 **Categories/Tags System đã sẵn sàng sử dụng!** 

Hệ thống này cung cấp nền tảng vững chắc cho việc tổ chức notes với phong cách retro độc đáo, tạo trải nghiệm người dùng mượt mà và trực quan.
